import requests

url = "https://zxts.zjzwfw.gov.cn/zlh/zsapi/xfpt/OTMmfvwtyz/OTMzfecm"


headers = {
    "Reference":"https://zxts.zjzwfw.gov.cn/zwmhww/index.html",
    "user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
    "Referer":"https://zxts.zjzwfw.gov.cn/"
}
data = {
  "bt": "",
  "bldw": "",
  "startTime": "",
  "endTime": "",
  "wtsdxzqhdm": "",
  "xfmd": "",
  "xfxs": "",
  "code": "33",
  "pageSize": 10,
  "pageNum": 3
}
response = requests.post(url, headers=headers, json = data)
print(response.text)